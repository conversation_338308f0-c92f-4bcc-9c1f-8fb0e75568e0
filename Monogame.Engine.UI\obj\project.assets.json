{"version": 3, "targets": {"net8.0-windows7.0": {"Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "MonoGame.Content.Builder.Task/3.8.1.303": {"type": "package", "build": {"build/_._": {}}}, "MonoGame.Framework.DesktopGL/3.8.1.303": {"type": "package", "compile": {"lib/net6.0/MonoGame.Framework.dll": {}}, "runtime": {"lib/net6.0/MonoGame.Framework.dll": {}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libSDL2-2.0.so.0": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libopenal.so.1": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libSDL2.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/osx/native/libopenal.1.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-x64/native/SDL2.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/soft_oal.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/SDL2.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/soft_oal.dll": {"assetType": "native", "rid": "win-x86"}}}, "Monogame.Engine.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "MonoGame.Content.Builder.Task": "3.8.1.303", "MonoGame.Framework.DesktopGL": "3.8.1.303"}, "compile": {"bin/placeholder/Monogame.Engine.Core.dll": {}}, "runtime": {"bin/placeholder/Monogame.Engine.Core.dll": {}}}}}, "libraries": {"Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "MonoGame.Content.Builder.Task/3.8.1.303": {"sha512": "9Ilzzje62LhWElbPNEl7vh7XsRSbze+lvCJdZtTZUGu48FRgvYN6THURwIB9PN98EI33/Wnf6iuShNUtD7hL4Q==", "type": "package", "path": "monogame.content.builder.task/3.8.1.303", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/MonoGame.Content.Builder.Task.props", "build/MonoGame.Content.Builder.Task.targets", "monogame.content.builder.task.3.8.1.303.nupkg.sha512", "monogame.content.builder.task.nuspec"]}, "MonoGame.Framework.DesktopGL/3.8.1.303": {"sha512": "eGYhqn0n1olk8MNYeE9EuBmoNNECN1T18rPMaQpkzsEQ0H3nVyFPXC+uCo78v5pi5juQpJ3PSFnSkjzZJ1U58A==", "type": "package", "path": "monogame.framework.desktopgl/3.8.1.303", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/MonoGame.Framework.DesktopGL.targets", "lib/net6.0/MonoGame.Framework.dll", "monogame.framework.desktopgl.3.8.1.303.nupkg.sha512", "monogame.framework.desktopgl.nuspec", "runtimes/linux-x64/native/libSDL2-2.0.so.0", "runtimes/linux-x64/native/libopenal.so.1", "runtimes/osx/native/libSDL2.dylib", "runtimes/osx/native/libopenal.1.dylib", "runtimes/win-x64/native/SDL2.dll", "runtimes/win-x64/native/soft_oal.dll", "runtimes/win-x86/native/SDL2.dll", "runtimes/win-x86/native/soft_oal.dll"]}, "Monogame.Engine.Core/1.0.0": {"type": "project", "path": "../Monogame.Engine.Core/Monogame.Engine.Core.csproj", "msbuildProject": "../Monogame.Engine.Core/Monogame.Engine.Core.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Monogame.Engine.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\Monogame.Engine.UI.csproj", "projectName": "Monogame.Engine.UI", "projectPath": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\Monogame.Engine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.Core\\Monogame.Engine.Core.csproj": {"projectPath": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.Core\\Monogame.Engine.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}