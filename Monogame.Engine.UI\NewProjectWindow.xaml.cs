using System;
using System.IO;
using System.Windows;
using Microsoft.Win32;

namespace Monogame.Engine.UI
{
	public partial class NewProjectWindow : Window
	{
		public string ProjectName => txtName.Text.Trim();
		public string TargetDirectory => txtLocation.Text.Trim();
		public string TemplateShortName
		{
			get
			{
				var item = cmbTemplate.SelectedItem as System.Windows.Controls.ComboBoxItem;
				return (item?.Tag as string) ?? "mgdesktopgl";
			}
		}
		public bool InitializeGit => chkGit.IsChecked == true;

		public NewProjectWindow()
		{
			InitializeComponent();
			// Default location
			txtLocation.Text = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
		}

		private void Browse_Click(object sender, RoutedEventArgs e)
		{
			var dlg = new OpenFileDialog
			{
				Title = "Select Folder",
				CheckFileExists = false,
				CheckPathExists = true,
				ValidateNames = false,
				FileName = "Select Folder"
			};
			if (dlg.ShowDialog() == true)
			{
				var dir = Path.GetDirectoryName(dlg.FileName);
				if (!string.IsNullOrEmpty(dir))
				{
					txtLocation.Text = dir;
				}
			}
		}

		private void Create_Click(object sender, RoutedEventArgs e)
		{
			if (string.IsNullOrWhiteSpace(ProjectName))
			{
				MessageBox.Show("Please enter a project name.");
				return;
			}
			if (string.IsNullOrWhiteSpace(TargetDirectory) || !Directory.Exists(TargetDirectory))
			{
				MessageBox.Show("Please select a valid target directory.");
				return;
			}
			DialogResult = true;
		}

		private void Cancel_Click(object sender, RoutedEventArgs e)
		{
			DialogResult = false;
		}
	}
}
