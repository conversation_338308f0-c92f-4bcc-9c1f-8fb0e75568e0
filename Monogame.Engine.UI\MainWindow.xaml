﻿<Window x:Class="Monogame.Engine.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="MonoGame Engine" Height="600" Width="900"
        WindowStartupLocation="CenterScreen">

    <!-- Styles (moved before usage to satisfy StaticResource resolution) -->
    <Window.Resources>
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="3" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0" Background="#F0F0F0" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1">
            <MenuItem Header="_File">
                <MenuItem Header="_New Project" Click="BtnNewProject_Click" InputGestureText="Ctrl+N"/>
                <MenuItem Header="_Open Project..." Click="BtnOpenProject_Click" InputGestureText="Ctrl+O"/>
                <Separator/>
                <MenuItem Header="Scan _Directory..." Click="BtnScanDirectory_Click"/>
                <MenuItem Header="Scan _Common Directories" Click="BtnScanCommon_Click"/>
                <Separator/>
                <MenuItem Header="E_xit" Click="Exit_Click" InputGestureText="Alt+F4"/>
            </MenuItem>
            <MenuItem Header="_Project">
                <MenuItem Header="_Validate Current Project" Click="ValidateCurrentProject_Click" IsEnabled="{Binding Engine.CurrentProject, RelativeSource={RelativeSource AncestorType=Window}}"/>
                <MenuItem Header="_Refresh Current Project" Click="RefreshCurrentProject_Click" IsEnabled="{Binding Engine.CurrentProject, RelativeSource={RelativeSource AncestorType=Window}}"/>
                <MenuItem Header="_Close Current Project" Click="CloseCurrentProject_Click" IsEnabled="{Binding Engine.CurrentProject, RelativeSource={RelativeSource AncestorType=Window}}"/>
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_About" Click="About_Click"/>
            </MenuItem>
        </Menu>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Project List -->
            <Border Grid.Column="0" Background="#F5F5F5" CornerRadius="5" Padding="15" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="Projects" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                    
                    <ScrollViewer x:Name="scrollProjects" VerticalScrollBarVisibility="Auto">
                        <ItemsControl x:Name="projectsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="White" CornerRadius="3" Padding="10" Margin="0,0,0,8" 
                                            BorderBrush="#DDDDDD" BorderThickness="1">
                                        <StackPanel>
                                            <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                            <TextBlock Text="{Binding TargetFramework}" Foreground="#666666" FontSize="12" Margin="0,2,0,0"/>
                                            <TextBlock Text="{Binding DirectoryPath}" Foreground="#888888" FontSize="10" Margin="0,2,0,0" 
                                                       TextTrimming="CharacterEllipsis"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </StackPanel>
            </Border>

            <!-- Right Panel - Project Details -->
            <Border Grid.Column="1" Background="#F5F5F5" CornerRadius="5" Padding="15">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="projectDetailsPanel">
                        <TextBlock Text="No Project Selected" FontSize="18" FontWeight="Bold" 
                                   Foreground="#666666" HorizontalAlignment="Center" Margin="0,50,0,0"/>
                        <TextBlock Text="Select a project from the left panel or open a new one to see details" 
                                   Foreground="#888888" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#2D2D30" Padding="15,8">
            <TextBlock x:Name="statusText" Text="Ready" Foreground="White" FontSize="12"/>
        </Border>
    </Grid>
</Window>
