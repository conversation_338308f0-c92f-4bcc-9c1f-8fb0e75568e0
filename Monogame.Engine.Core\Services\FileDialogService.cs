using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Monogame.Engine.Core.Services
{
    public class FileDialogService
    {
        /// <summary>
        /// Gets a list of recent directories from common locations
        /// </summary>
        /// <returns>List of recent directory paths</returns>
        public async Task<List<string>> GetRecentDirectoriesAsync()
        {
            return await Task.Run(() =>
            {
                var directories = new List<string>();

                try
                {
                    // Add common development directories
                    var commonPaths = new[]
                    {
                        Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                        Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Downloads"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Source"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Projects"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "GitHub"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Repos")
                    };

                    foreach (var path in commonPaths)
                    {
                        if (!string.IsNullOrEmpty(path) && Directory.Exists(path))
                        {
                            directories.Add(path);
                        }
                    }

                    // Add Visual Studio default project locations if they exist
                    var vsPaths = new[]
                    {
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Visual Studio 2022"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Visual Studio 2019"),
                        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Visual Studio 2017")
                    };

                    foreach (var vsPath in vsPaths)
                    {
                        if (Directory.Exists(vsPath))
                        {
                            var projectsPath = Path.Combine(vsPath, "Projects");
                            if (Directory.Exists(projectsPath))
                            {
                                directories.Add(projectsPath);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error getting recent directories: {ex.Message}");
                }

                return directories;
            });
        }

        /// <summary>
        /// Validates if a path is a valid directory
        /// </summary>
        /// <param name="path">Path to validate</param>
        /// <returns>True if the path is a valid directory</returns>
        public async Task<bool> IsValidDirectoryAsync(string path)
        {
            return await Task.Run(() =>
            {
                try
                {
                    return !string.IsNullOrEmpty(path) && Directory.Exists(path);
                }
                catch
                {
                    return false;
                }
            });
        }

        /// <summary>
        /// Gets the parent directory of a given path
        /// </summary>
        /// <param name="path">Path to get parent directory from</param>
        /// <returns>Parent directory path or null if not available</returns>
        public async Task<string?> GetParentDirectoryAsync(string path)
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (string.IsNullOrEmpty(path))
                        return null;

                    var directory = Path.GetDirectoryName(path);
                    return !string.IsNullOrEmpty(directory) && Directory.Exists(directory) ? directory : null;
                }
                catch
                {
                    return null;
                }
            });
        }

        /// <summary>
        /// Gets the display name for a directory (last part of the path)
        /// </summary>
        /// <param name="path">Directory path</param>
        /// <returns>Display name for the directory</returns>
        public async Task<string> GetDirectoryDisplayNameAsync(string path)
        {
            return await Task.Run(() =>
            {
                try
                {
                    if (string.IsNullOrEmpty(path))
                        return "Unknown";

                    var directoryName = Path.GetFileName(path);
                    return !string.IsNullOrEmpty(directoryName) ? directoryName : path;
                }
                catch
                {
                    return "Unknown";
                }
            });
        }

        /// <summary>
        /// Gets all .csproj files in a directory (for manual browsing)
        /// </summary>
        /// <param name="directoryPath">Directory to search</param>
        /// <param name="recursive">Whether to search recursively</param>
        /// <returns>List of .csproj file paths</returns>
        public async Task<List<string>> GetProjectFilesAsync(string directoryPath, bool recursive = true)
        {
            return await Task.Run(() =>
            {
                var projectFiles = new List<string>();

                try
                {
                    if (recursive)
                    {
                        projectFiles.AddRange(Directory.GetFiles(directoryPath, "*.csproj", SearchOption.AllDirectories));
                    }
                    else
                    {
                        projectFiles.AddRange(Directory.GetFiles(directoryPath, "*.csproj", SearchOption.TopDirectoryOnly));
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error getting project files from {directoryPath}: {ex.Message}");
                }

                return projectFiles;
            });
        }
    }
}
