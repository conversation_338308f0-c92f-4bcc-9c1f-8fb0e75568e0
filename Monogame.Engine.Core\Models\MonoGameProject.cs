using System;
using System.Collections.Generic;
using System.IO;
using System.Xml.Linq;
using System.Linq; // Added for .SelectMany() and .Where()

namespace Monogame.Engine.Core.Models
{
    public class MonoGameProject
    {
        public string Name { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string DirectoryPath { get; set; } = string.Empty;
        public string TargetFramework { get; set; } = string.Empty;
        public string OutputType { get; set; } = string.Empty;
        public List<string> SourceFiles { get; set; } = new();
        public List<string> ContentFiles { get; set; } = new();
        public List<string> References { get; set; } = new();
        public DateTime LastModified { get; set; }
        public bool IsValid { get; set; }

        public static MonoGameProject? LoadFromFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath) || !Path.GetExtension(filePath).Equals(".csproj", StringComparison.OrdinalIgnoreCase))
                    return null;

                var project = new MonoGameProject
                {
                    FilePath = filePath,
                    DirectoryPath = Path.GetDirectoryName(filePath) ?? string.Empty,
                    Name = Path.GetFileNameWithoutExtension(filePath),
                    LastModified = File.GetLastWriteTime(filePath)
                };

                var doc = XDocument.Load(filePath);
                var root = doc.Root;
                if (root == null) return null;

                // Extract basic project properties
                var propertyGroup = root.Element("PropertyGroup");
                if (propertyGroup != null)
                {
                    project.TargetFramework = propertyGroup.Element("TargetFramework")?.Value ?? string.Empty;
                    project.OutputType = propertyGroup.Element("OutputType")?.Value ?? string.Empty;
                }

                // Extract source files
                var compileItems = root.Elements("ItemGroup")
                    .SelectMany(ig => ig.Elements("Compile"))
                    .Select(c => c.Attribute("Include")?.Value)
                    .Where(v => !string.IsNullOrEmpty(v))
                    .ToList();

                project.SourceFiles.AddRange(compileItems!);

                // Extract content files
                var contentItems = root.Elements("ItemGroup")
                    .SelectMany(ig => ig.Elements("Content"))
                    .Select(c => c.Attribute("Include")?.Value)
                    .Where(v => !string.IsNullOrEmpty(v))
                    .ToList();

                project.ContentFiles.AddRange(contentItems!);

                // Extract references
                var referenceItems = root.Elements("ItemGroup")
                    .SelectMany(ig => ig.Elements("Reference"))
                    .Select(r => r.Attribute("Include")?.Value)
                    .Where(v => !string.IsNullOrEmpty(v))
                    .ToList();

                project.References.AddRange(referenceItems!);

                // Check if this is a valid MonoGame project
                project.IsValid = IsMonoGameProject(root);

                return project;
            }
            catch
            {
                return null;
            }
        }

        private static bool IsMonoGameProject(XElement root)
        {
            // Check for MonoGame-specific elements or references
            var hasMonoGameReference = root.Elements("ItemGroup")
                .SelectMany(ig => ig.Elements("Reference"))
                .Any(r => r.Attribute("Include")?.Value?.Contains("MonoGame") == true);

            var hasMonoGamePackage = root.Elements("ItemGroup")
                .SelectMany(ig => ig.Elements("PackageReference"))
                .Any(p => p.Attribute("Include")?.Value?.Contains("MonoGame") == true);

            return hasMonoGameReference || hasMonoGamePackage;
        }

        public override string ToString()
        {
            return $"{Name} ({TargetFramework})";
        }
    }
}
