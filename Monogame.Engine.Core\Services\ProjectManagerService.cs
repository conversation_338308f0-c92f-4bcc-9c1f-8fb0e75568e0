using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Monogame.Engine.Core.Models;

namespace Monogame.Engine.Core.Services
{
    public class ProjectManagerService
    {
        private MonoGameProject? _currentProject;
        private readonly List<MonoGameProject> _recentProjects;
        private readonly int _maxRecentProjects = 20;

        public event EventHandler<MonoGameProject>? ProjectOpened;
        public event EventHandler<MonoGameProject>? ProjectClosed;
        public event EventHandler<List<MonoGameProject>>? RecentProjectsChanged;

        public MonoGameProject? CurrentProject => _currentProject;
        public IReadOnlyList<MonoGameProject> RecentProjects => _recentProjects.AsReadOnly();

        public ProjectManagerService()
        {
            _recentProjects = new List<MonoGameProject>();
        }

        /// <summary>
        /// Opens a MonoGame project from a file path
        /// </summary>
        /// <param name="projectFilePath">Path to the .csproj file</param>
        /// <returns>True if the project was successfully opened</returns>
        public async Task<bool> OpenProjectAsync(string projectFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(projectFilePath) || !File.Exists(projectFilePath))
                    return false;

                // Close current project if any
                if (_currentProject != null)
                {
                    await CloseProjectAsync();
                }

                // Load the new project
                var project = await Task.Run(() => MonoGameProject.LoadFromFile(projectFilePath));
                if (project == null || !project.IsValid)
                    return false;

                _currentProject = project;
                AddToRecentProjects(project);
                ProjectOpened?.Invoke(this, project);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error opening project {projectFilePath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Closes the currently open project
        /// </summary>
        public async Task CloseProjectAsync()
        {
            if (_currentProject == null)
                return;

            var projectToClose = _currentProject;
            _currentProject = null;
            
            await Task.Run(() => ProjectClosed?.Invoke(this, projectToClose));
        }

        /// <summary>
        /// Validates the current project and returns any issues
        /// </summary>
        /// <returns>List of validation issues, empty if project is valid</returns>
        public async Task<List<string>> ValidateCurrentProjectAsync()
        {
            if (_currentProject == null)
                return new List<string> { "No project is currently open" };

            var issues = new List<string>();

            try
            {
                // Check if project file still exists
                if (!File.Exists(_currentProject.FilePath))
                {
                    issues.Add("Project file no longer exists");
                    return issues;
                }

                // Check if source files exist
                foreach (var sourceFile in _currentProject.SourceFiles)
                {
                    var fullPath = Path.Combine(_currentProject.DirectoryPath, sourceFile);
                    if (!File.Exists(fullPath))
                    {
                        issues.Add($"Source file not found: {sourceFile}");
                    }
                }

                // Check if content files exist
                foreach (var contentFile in _currentProject.ContentFiles)
                {
                    var fullPath = Path.Combine(_currentProject.DirectoryPath, contentFile);
                    if (!File.Exists(fullPath))
                    {
                        issues.Add($"Content file not found: {contentFile}");
                    }
                }

                // Check for common MonoGame project structure
                var contentDir = Path.Combine(_currentProject.DirectoryPath, "Content");
                if (!Directory.Exists(contentDir))
                {
                    issues.Add("Content directory not found (typical MonoGame project structure)");
                }

                var gameFile = _currentProject.SourceFiles.FirstOrDefault(f => 
                    f.EndsWith("Game.cs", StringComparison.OrdinalIgnoreCase) ||
                    f.EndsWith("Game1.cs", StringComparison.OrdinalIgnoreCase));

                if (string.IsNullOrEmpty(gameFile))
                {
                    issues.Add("No Game.cs or Game1.cs file found (typical MonoGame entry point)");
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Error during validation: {ex.Message}");
            }

            return await Task.FromResult(issues);
        }

        /// <summary>
        /// Refreshes the current project data
        /// </summary>
        /// <returns>True if refresh was successful</returns>
        public async Task<bool> RefreshCurrentProjectAsync()
        {
            if (_currentProject == null)
                return false;

            try
            {
                var refreshedProject = await Task.Run(() => MonoGameProject.LoadFromFile(_currentProject.FilePath));
                if (refreshedProject != null && refreshedProject.IsValid)
                {
                    var oldProject = _currentProject;
                    _currentProject = refreshedProject;
                    
                    // Update in recent projects
                    var recentIndex = _recentProjects.FindIndex(p => p.FilePath == oldProject.FilePath);
                    if (recentIndex >= 0)
                    {
                        _recentProjects[recentIndex] = refreshedProject;
                        RecentProjectsChanged?.Invoke(this, _recentProjects);
                    }

                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets project statistics
        /// </summary>
        /// <returns>Project statistics as a dictionary</returns>
        public async Task<Dictionary<string, object>> GetProjectStatisticsAsync()
        {
            if (_currentProject == null)
                return new Dictionary<string, object>();

            var stats = new Dictionary<string, object>
            {
                ["Name"] = _currentProject.Name,
                ["TargetFramework"] = _currentProject.TargetFramework,
                ["OutputType"] = _currentProject.OutputType,
                ["SourceFileCount"] = _currentProject.SourceFiles.Count,
                ["ContentFileCount"] = _currentProject.ContentFiles.Count,
                ["ReferenceCount"] = _currentProject.References.Count,
                ["LastModified"] = _currentProject.LastModified,
                ["DirectorySize"] = await GetDirectorySizeAsync(_currentProject.DirectoryPath)
            };

            return stats;
        }

        /// <summary>
        /// Adds a project to the recent projects list
        /// </summary>
        /// <param name="project">Project to add</param>
        private void AddToRecentProjects(MonoGameProject project)
        {
            // Remove if already exists
            _recentProjects.RemoveAll(p => p.FilePath == project.FilePath);

            // Add to beginning
            _recentProjects.Insert(0, project);

            // Keep only the most recent projects
            if (_recentProjects.Count > _maxRecentProjects)
            {
                _recentProjects.RemoveRange(_maxRecentProjects, _recentProjects.Count - _maxRecentProjects);
            }

            RecentProjectsChanged?.Invoke(this, _recentProjects);
        }

        /// <summary>
        /// Gets the size of a directory in bytes
        /// </summary>
        /// <param name="directoryPath">Path to the directory</param>
        /// <returns>Size in bytes</returns>
        private async Task<long> GetDirectorySizeAsync(string directoryPath)
        {
            try
            {
                var dir = new DirectoryInfo(directoryPath);
                return await Task.Run(() => GetDirectorySize(dir));
            }
            catch
            {
                return 0;
            }
        }

        private long GetDirectorySize(DirectoryInfo dir)
        {
            long size = 0;

            try
            {
                // Add file sizes
                foreach (var file in dir.GetFiles())
                {
                    size += file.Length;
                }

                // Add subdirectory sizes
                foreach (var subDir in dir.GetDirectories())
                {
                    size += GetDirectorySize(subDir);
                }
            }
            catch
            {
                // Ignore access denied errors
            }

            return size;
        }
    }
}
