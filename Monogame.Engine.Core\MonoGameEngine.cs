using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Monogame.Engine.Core.Models;
using Monogame.Engine.Core.Services;

namespace Monogame.Engine.Core
{
    /// <summary>
    /// Main engine class for browsing and managing MonoGame projects
    /// </summary>
    public class MonoGameEngine : IDisposable
    {
        private readonly ProjectBrowserService _projectBrowser;
        private readonly ProjectManagerService _projectManager;
        private readonly FileDialogService _fileDialog;
        private readonly ProjectCreationService _projectCreator;

        public event EventHandler<MonoGameProject>? ProjectOpened;
        public event EventHandler<MonoGameProject>? ProjectClosed;
        public event EventHandler<List<MonoGameProject>>? RecentProjectsChanged;

        public MonoGameProject? CurrentProject => _projectManager.CurrentProject;
        public IReadOnlyList<MonoGameProject> RecentProjects => _projectManager.RecentProjects;

        public MonoGameEngine()
        {
            _projectBrowser = new ProjectBrowserService();
            _projectManager = new ProjectManagerService();
            _fileDialog = new FileDialogService();
            _projectCreator = new ProjectCreationService();

            // Wire up events
            _projectManager.ProjectOpened += (sender, project) => ProjectOpened?.Invoke(this, project);
            _projectManager.ProjectClosed += (sender, project) => ProjectClosed?.Invoke(this, project);
            _projectManager.RecentProjectsChanged += (sender, projects) => RecentProjectsChanged?.Invoke(this, projects);
        }

        /// <summary>
        /// Opens a MonoGame project from a file path
        /// </summary>
        /// <param name="projectFilePath">Path to the .csproj file</param>
        /// <returns>True if the project was successfully opened</returns>
        public async Task<bool> OpenProjectAsync(string projectFilePath)
        {
            return await _projectManager.OpenProjectAsync(projectFilePath);
        }

        /// <summary>
        /// Creates a new MonoGame project via dotnet templates
        /// </summary>
        public async Task<(bool success, string? projectPath, string output)> CreateProjectAsync(ProjectCreationService.CreateProjectOptions options)
        {
            return await _projectCreator.CreateProjectAsync(options);
        }

        /// <summary>
        /// Ensures MonoGame dotnet new templates are installed
        /// </summary>
        public async Task<bool> EnsureMonoGameTemplatesInstalledAsync()
        {
            return await _projectCreator.EnsureMonoGameTemplatesInstalledAsync();
        }

        /// <summary>
        /// Closes the currently open project
        /// </summary>
        public async Task CloseProjectAsync()
        {
            await _projectManager.CloseProjectAsync();
        }

        /// <summary>
        /// Scans a directory for MonoGame projects
        /// </summary>
        /// <param name="directoryPath">Directory to scan</param>
        /// <param name="recursive">Whether to scan subdirectories recursively</param>
        /// <returns>List of found MonoGame projects</returns>
        public async Task<List<MonoGameProject>> ScanDirectoryAsync(string directoryPath, bool recursive = true)
        {
            return await _projectBrowser.ScanDirectoryAsync(directoryPath, recursive);
        }

        /// <summary>
        /// Scans common development directories for MonoGame projects
        /// </summary>
        /// <returns>List of found MonoGame projects</returns>
        public async Task<List<MonoGameProject>> ScanCommonDirectoriesAsync()
        {
            return await _projectBrowser.ScanCommonDirectoriesAsync();
        }

        /// <summary>
        /// Gets recent projects from a specified directory
        /// </summary>
        /// <param name="directoryPath">Directory to scan</param>
        /// <param name="count">Maximum number of recent projects to return</param>
        /// <returns>List of recent MonoGame projects</returns>
        public async Task<List<MonoGameProject>> GetRecentProjectsAsync(string directoryPath, int count = 10)
        {
            return await _projectBrowser.GetRecentProjectsAsync(directoryPath, count);
        }

        /// <summary>
        /// Validates the current project and returns any issues
        /// </summary>
        /// <returns>List of validation issues, empty if project is valid</returns>
        public async Task<List<string>> ValidateCurrentProjectAsync()
        {
            return await _projectManager.ValidateCurrentProjectAsync();
        }

        /// <summary>
        /// Refreshes the current project data
        /// </summary>
        /// <returns>True if refresh was successful</returns>
        public async Task<bool> RefreshCurrentProjectAsync()
        {
            return await _projectManager.RefreshCurrentProjectAsync();
        }

        /// <summary>
        /// Gets project statistics for the current project
        /// </summary>
        /// <returns>Project statistics as a dictionary</returns>
        public async Task<Dictionary<string, object>> GetProjectStatisticsAsync()
        {
            return await _projectManager.GetProjectStatisticsAsync();
        }

        /// <summary>
        /// Gets a list of recent directories from common locations
        /// </summary>
        /// <returns>List of recent directory paths</returns>
        public async Task<List<string>> GetRecentDirectoriesAsync()
        {
            return await _fileDialog.GetRecentDirectoriesAsync();
        }

        /// <summary>
        /// Validates if a file path is a valid MonoGame project
        /// </summary>
        /// <param name="filePath">Path to the project file</param>
        /// <returns>True if it's a valid MonoGame project</returns>
        public async Task<bool> IsValidMonoGameProjectAsync(string filePath)
        {
            return await _projectBrowser.IsValidMonoGameProjectAsync(filePath);
        }

        /// <summary>
        /// Gets the display name for a directory
        /// </summary>
        /// <param name="path">Directory path</param>
        /// <returns>Display name for the directory</returns>
        public async Task<string> GetDirectoryDisplayNameAsync(string path)
        {
            return await _fileDialog.GetDirectoryDisplayNameAsync(path);
        }

        /// <summary>
        /// Gets the parent directory of a given path
        /// </summary>
        /// <param name="path">Path to get parent directory from</param>
        /// <returns>Parent directory path or null if not available</returns>
        public async Task<string?> GetParentDirectoryAsync(string path)
        {
            return await _fileDialog.GetParentDirectoryAsync(path);
        }

        /// <summary>
        /// Gets all project files in a directory
        /// </summary>
        /// <param name="directoryPath">Directory to search</param>
        /// <param name="recursive">Whether to search recursively</param>
        /// <returns>List of project file paths</returns>
        public async Task<List<string>> GetProjectFilesAsync(string directoryPath, bool recursive = true)
        {
            return await _fileDialog.GetProjectFilesAsync(directoryPath, recursive);
        }

        public void Dispose()
        {
            // Clean up resources if needed
            GC.SuppressFinalize(this);
        }
    }
}
