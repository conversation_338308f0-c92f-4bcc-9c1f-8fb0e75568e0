# Simplified Graphics Library Structure Generator
# Run this script from inside your existing project directory

param(
    [string]$RootNamespace = "MonoGameEngine.Graphics"
)

Write-Host "Creating Graphics Library folders and files..." -ForegroundColor Green

# Define the folder structure
$folders = @(
    "Core",
    "Rendering\RenderTarget",
    "Sprites",
    "Textures", 
    "Animation\Easing",
    "Camera\CameraEffects",
    "Lighting",
    "Particles\Modifiers",
    "Effects\PostProcessing",
    "Effects\Shaders\BuiltinShaders",
    "Effects\Transitions",
    "UI\Rendering",
    "UI\Utilities",
    "Debug",
    "Math",
    "Assets",
    "Utilities"
)

# Create all folders
foreach ($folder in $folders) {
    if (!(Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force | Out-Null
        Write-Host "Created: $folder" -ForegroundColor Yellow
    }
}

# Function to create a basic C# class file
function New-CSharpFile {
    param($Path, $ClassName, $Namespace, $Type = "class")
    
    $filePath = Join-Path $Path "$ClassName.cs"
    if (Test-Path $filePath) { return }
    
    $content = @"
using System;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;

namespace $Namespace
{
    public $Type $ClassName
    {
        
    }
}
"@
    Set-Content -Path $filePath -Value $content
    Write-Host "Created: $ClassName.cs" -ForegroundColor Cyan
}

# Create all files
$files = @{
    "Core" = @("IRenderer:interface", "IDrawable:interface", "IRenderLayer:interface", "GraphicsContext")
    "Rendering" = @("SpriteBatchRenderer", "PrimitiveRenderer", "LayeredRenderer", "RenderQueue")
    "Rendering\RenderTarget" = @("RenderTargetManager", "IRenderTarget:interface", "CustomRenderTarget")
    "Sprites" = @("Sprite", "SpriteFrame", "AnimatedSprite", "SpriteSheet", "SpriteAtlas")
    "Textures" = @("TextureManager", "TextureCache", "TextureLoader", "TextureUtilities")
    "Animation" = @("AnimationClip", "AnimationFrame", "AnimationPlayer", "FrameAnimation")
    "Animation\Easing" = @("EasingFunctions", "ITweener:interface")
    "Camera" = @("Camera2D", "CameraController", "ViewportManager")
    "Camera\CameraEffects" = @("CameraShake", "CameraFollow")
    "Lighting" = @("Light2D", "LightManager", "ShadowCaster", "LightingRenderer")
    "Particles" = @("ParticleSystem", "Particle", "ParticleEmitter", "ParticlePool")
    "Particles\Modifiers" = @("IParticleModifier:interface", "VelocityModifier", "ColorModifier", "ScaleModifier")
    "Effects\PostProcessing" = @("IPostProcessor:interface", "BloomEffect", "BlurEffect", "ColorGradingEffect")
    "Effects\Shaders" = @("ShaderManager", "ShaderProgram")
    "Effects\Transitions" = @("ITransitionEffect:interface", "FadeTransition", "SlideTransition", "WipeTransition")
    "UI\Rendering" = @("UIRenderer", "NinePatch", "TextRenderer")
    "UI\Utilities" = @("UIHelpers", "LayoutCalculator")
    "Debug" = @("DebugRenderer", "WireframeRenderer", "BoundingBoxRenderer", "PerformanceOverlay")
    "Math" = @("Transform2D", "Rectangle2D", "MathExtensions", "InterpolationHelper")
    "Assets" = @("GraphicsAsset", "SpriteAsset", "AnimationAsset", "MaterialAsset")
    "Utilities" = @("ColorUtilities", "GraphicsHelper", "ScreenUtilities", "BatchingHelper")
}

foreach ($folder in $files.Keys) {
    $namespace = "$RootNamespace." + ($folder -replace "\\", ".")
    foreach ($fileInfo in $files[$folder]) {
        $parts = $fileInfo -split ":"
        $className = $parts[0]
        $classType = if ($parts.Length -gt 1) { $parts[1] } else { "class" }
        New-CSharpFile -Path $folder -ClassName $className -Namespace $namespace -Type $classType
    }
}

# Create basic shader files
$shaders = @("SpriteShader", "LightingShader", "ParticleShader")
foreach ($shader in $shaders) {
    $shaderPath = "Effects\Shaders\BuiltinShaders\$shader.fx"
    if (!(Test-Path $shaderPath)) {
        Set-Content -Path $shaderPath -Value "// $shader shader - TODO: Implement"
        Write-Host "Created: $shader.fx" -ForegroundColor Magenta
    }
}

Write-Host "`nDone! Created folder structure and basic class files." -ForegroundColor Green