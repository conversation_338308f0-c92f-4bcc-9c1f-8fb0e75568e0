# MonoGame Engine

A WPF-based engine for browsing, opening, and managing MonoGame projects. This engine provides a user-friendly interface to discover and work with existing MonoGame projects on your system.

## Features

### Core Engine (`Monogame.Engine.Core`)
- **Project Discovery**: Scan directories recursively for MonoGame projects
- **Project Validation**: Validate project structure and identify potential issues
- **Project Management**: Open, close, and manage MonoGame projects
- **File System Integration**: Browse file system with modern dialogs
- **Common Directory Scanning**: Automatically scan common development directories

### WPF UI (`Monogame.Engine.UI`)
- **Modern Interface**: Clean, responsive WPF interface
- **Project Browser**: List and browse discovered MonoGame projects
- **Project Details**: View comprehensive project information
- **Project Actions**: Open and validate projects directly from the UI
- **Status Updates**: Real-time status information and error handling

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 or later (recommended)
- MonoGame projects to browse (optional)

### Building the Solution
1. Open `Monogame.Engine.sln` in Visual Studio
2. Restore NuGet packages
3. Build the solution (Build > Build Solution)

### Running the Application
1. Set `Monogame.Engine.UI` as the startup project
2. Press F5 or click "Start Debugging"

## Usage

### Opening a Project
1. Click **"Open Project"** to browse for a `.csproj` file
2. Select a MonoGame project file
3. The project will be loaded and displayed in the details panel

### Scanning for Projects
1. **Scan Directory**: Click **"Scan Directory"** to select a folder to scan
2. **Scan Common**: Click **"Scan Common"** to automatically scan common development directories
3. Found projects will appear in the left panel

### Project Information
The engine displays comprehensive project information including:
- Project name and target framework
- Source file count and content file count
- References and dependencies
- Last modified date
- Project validation status

### Project Validation
Click **"Validate Project"** on any project to check for:
- Missing source files
- Missing content files
- Project structure issues
- MonoGame-specific requirements

## Architecture

### Core Services
- **ProjectBrowserService**: Scans directories for MonoGame projects
- **ProjectManagerService**: Manages project lifecycle and validation
- **FileDialogService**: Handles file and folder selection dialogs

### Models
- **MonoGameProject**: Represents a MonoGame project with metadata

### Main Engine
- **MonoGameEngine**: Coordinates all services and provides the main API

## Supported Project Types

The engine recognizes MonoGame projects by detecting:
- MonoGame package references in `.csproj` files
- MonoGame assembly references
- Standard MonoGame project structure

## Common Directories Scanned

The engine automatically scans these common locations:
- Documents folder
- Desktop folder
- Downloads folder
- Source/Projects folders
- GitHub/Repos folders
- Visual Studio project folders

## Error Handling

The engine includes comprehensive error handling:
- File system access errors
- Invalid project files
- Missing dependencies
- Project validation issues

## Future Enhancements

Potential future features:
- Project templates and creation
- Build and run integration
- Content pipeline management
- Asset browsing and management
- Project comparison tools
- Export and import functionality

## Contributing

This is a foundation for a MonoGame development engine. Feel free to extend it with additional features and improvements.

## License

This project is provided as-is for educational and development purposes.
