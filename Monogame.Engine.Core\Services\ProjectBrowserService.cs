using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Monogame.Engine.Core.Models;

namespace Monogame.Engine.Core.Services
{
    public class ProjectBrowserService
    {
        /// <summary>
        /// Scans a directory for MonoGame projects
        /// </summary>
        /// <param name="directoryPath">The directory to scan</param>
        /// <param name="recursive">Whether to scan subdirectories recursively</param>
        /// <returns>List of found MonoGame projects</returns>
        public async Task<List<MonoGameProject>> ScanDirectoryAsync(string directoryPath, bool recursive = true)
        {
            if (!Directory.Exists(directoryPath))
                return new List<MonoGameProject>();

            var projects = new List<MonoGameProject>();

            try
            {
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var projectFiles = Directory.GetFiles(directoryPath, "*.csproj", searchOption);
                
                foreach (var projectFile in projectFiles)
                {
                    var project = await Task.Run(() => MonoGameProject.LoadFromFile(projectFile));
                    if (project?.IsValid == true)
                    {
                        projects.Add(project);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error if needed
                System.Diagnostics.Debug.WriteLine($"Error scanning directory {directoryPath}: {ex.Message}");
            }

            return projects.OrderBy(p => p.Name).ToList();
        }

        /// <summary>
        /// Scans multiple directories for MonoGame projects
        /// </summary>
        /// <param name="directoryPaths">List of directories to scan</param>
        /// <param name="recursive">Whether to scan subdirectories recursively</param>
        /// <returns>Combined list of found MonoGame projects</returns>
        public async Task<List<MonoGameProject>> ScanDirectoriesAsync(IEnumerable<string> directoryPaths, bool recursive = true)
        {
            var allProjects = new List<MonoGameProject>();

            foreach (var directoryPath in directoryPaths)
            {
                var projects = await ScanDirectoryAsync(directoryPath, recursive);
                allProjects.AddRange(projects);
            }

            return allProjects.OrderBy(p => p.Name).ToList();
        }

        /// <summary>
        /// Scans common development directories for MonoGame projects
        /// </summary>
        /// <returns>List of found MonoGame projects</returns>
        public async Task<List<MonoGameProject>> ScanCommonDirectoriesAsync()
        {
            var commonDirectories = new List<string>();

            // Add user's Documents folder
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            if (!string.IsNullOrEmpty(documentsPath))
            {
                commonDirectories.Add(documentsPath);
            }

            // Add user's Desktop folder
            var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            if (!string.IsNullOrEmpty(desktopPath))
            {
                commonDirectories.Add(desktopPath);
            }

            // Add user's Downloads folder
            var downloadsPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            if (!string.IsNullOrEmpty(downloadsPath))
            {
                var downloads = Path.Combine(downloadsPath, "Downloads");
                if (Directory.Exists(downloads))
                {
                    commonDirectories.Add(downloads);
                }
            }

            return await ScanDirectoriesAsync(commonDirectories, true);
        }

        /// <summary>
        /// Validates if a file path is a valid MonoGame project
        /// </summary>
        /// <param name="filePath">Path to the project file</param>
        /// <returns>True if it's a valid MonoGame project</returns>
        public async Task<bool> IsValidMonoGameProjectAsync(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                return false;

            var project = await Task.Run(() => MonoGameProject.LoadFromFile(filePath));
            return project?.IsValid == true;
        }

        /// <summary>
        /// Gets recent projects from a specified directory (based on last modified date)
        /// </summary>
        /// <param name="directoryPath">Directory to scan</param>
        /// <param name="count">Maximum number of recent projects to return</param>
        /// <returns>List of recent MonoGame projects</returns>
        public async Task<List<MonoGameProject>> GetRecentProjectsAsync(string directoryPath, int count = 10)
        {
            var allProjects = await ScanDirectoryAsync(directoryPath, true);
            return allProjects
                .OrderByDescending(p => p.LastModified)
                .Take(count)
                .ToList();
        }
    }
}
