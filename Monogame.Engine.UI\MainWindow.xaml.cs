﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using System.IO;
using Monogame.Engine.Core;
using Monogame.Engine.Core.Models;
using Monogame.Engine.Core.Services;

namespace Monogame.Engine.UI
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public readonly MonoGameEngine _engine;
        private List<MonoGameProject> _allProjects;

        public MainWindow()
        {
            InitializeComponent();
            
            _engine = new MonoGameEngine();
            _allProjects = new List<MonoGameProject>();

            // Wire up engine events
            _engine.ProjectOpened += OnProjectOpened;
            _engine.ProjectClosed += OnProjectClosed;
            _engine.RecentProjectsChanged += OnRecentProjectsChanged;

            // Initialize UI
            UpdateStatus("Ready - Use the File menu to browse or scan for MonoGame projects");
        }

        private async void BtnNewProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dlg = new NewProjectWindow { Owner = this };
                if (dlg.ShowDialog() != true)
                {
                    return;
                }

                UpdateStatus("Ensuring MonoGame templates are installed...");
                
                var templatesOk = await _engine.EnsureMonoGameTemplatesInstalledAsync();
                if (!templatesOk)
                {
                    UpdateStatus("Failed to install MonoGame templates.");
                    MessageBox.Show("Failed to install MonoGame templates. Make sure .NET SDK is installed and try again.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                UpdateStatus("Creating project...");
                var options = new ProjectCreationService.CreateProjectOptions
                {
                    ProjectName = dlg.ProjectName,
                    TargetDirectory = dlg.TargetDirectory,
                    TemplateShortName = dlg.TemplateShortName,
                    InitializeGitRepository = dlg.InitializeGit
                };

                var (success, projectPath, output) = await _engine.CreateProjectAsync(options);
                if (!success || string.IsNullOrEmpty(projectPath))
                {
                    UpdateStatus("Project creation failed.");
                    MessageBox.Show(output ?? "Project creation failed.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                UpdateStatus("Opening created project...");
                // Find csproj in created directory
                var csproj = Directory.GetFiles(projectPath!, "*.csproj", SearchOption.TopDirectoryOnly).FirstOrDefault();
                if (csproj == null)
                {
                    UpdateStatus("Created project, but .csproj was not found.");
                    MessageBox.Show("Created project, but .csproj was not found.", "Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var opened = await _engine.OpenProjectAsync(csproj);
                if (opened)
                {
                    UpdateStatus($"Project created and opened: {Path.GetFileNameWithoutExtension(csproj)}");
                    DisplayProjectDetails(_engine.CurrentProject);
                }
                else
                {
                    UpdateStatus("Project created but failed to open");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error creating project: {ex.Message}");
                MessageBox.Show($"Error creating project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnOpenProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("Opening project dialog...");
                
                var openFileDialog = new OpenFileDialog
                {
                    Title = "Open MonoGame Project",
                    Filter = "C# Project Files (*.csproj)|*.csproj|All Files (*.*)|*.*",
                    DefaultExt = "csproj",
                    CheckFileExists = true,
                    CheckPathExists = true,
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
                };

                var result = openFileDialog.ShowDialog();
                if (result == true)
                {
                    var success = await _engine.OpenProjectAsync(openFileDialog.FileName);
                    
                    if (success)
                    {
                        UpdateStatus($"Project opened: {_engine.CurrentProject?.Name}");
                        DisplayProjectDetails(_engine.CurrentProject);
                    }
                    else
                    {
                        UpdateStatus("Project could not be opened - may not be a valid MonoGame project");
                    }
                }
                else
                {
                    UpdateStatus("No project selected");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening project: {ex.Message}");
                MessageBox.Show($"Error opening project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnScanDirectory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("Opening directory selection dialog...");
                
                // Use a workaround for folder selection since WPF doesn't have a built-in folder browser
                var openFileDialog = new OpenFileDialog
                {
                    Title = "Select Directory to Scan",
                    ValidateNames = false,
                    CheckFileExists = false,
                    CheckPathExists = true,
                    FileName = "Select Folder"
                };

                var result = openFileDialog.ShowDialog();
                if (result == true)
                {
                    var fileName = openFileDialog.FileName;
                    var directoryPath = Path.GetDirectoryName(fileName);
                    
                    if (!string.IsNullOrEmpty(directoryPath))
                    {
                        UpdateStatus($"Scanning directory: {directoryPath}");
                        var projects = await _engine.ScanDirectoryAsync(directoryPath, true);
                        
                        if (projects.Any())
                        {
                            _allProjects = projects;
                            DisplayProjectsList(projects);
                            UpdateStatus($"Found {projects.Count} MonoGame project(s)");
                        }
                        else
                        {
                            UpdateStatus("No MonoGame projects found in selected directory");
                        }
                    }
                }
                else
                {
                    UpdateStatus("No directory selected");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error scanning directory: {ex.Message}");
                MessageBox.Show($"Error scanning directory: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnScanCommon_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("Scanning common directories for MonoGame projects...");
                
                var projects = await _engine.ScanCommonDirectoriesAsync();
                
                if (projects.Any())
                {
                    _allProjects = projects;
                    DisplayProjectsList(projects);
                    UpdateStatus($"Found {projects.Count} MonoGame project(s) in common directories");
                }
                else
                {
                    UpdateStatus("No MonoGame projects found in common directories");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error scanning common directories: {ex.Message}");
                MessageBox.Show($"Error scanning common directories: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ValidateCurrentProject_Click(object sender, RoutedEventArgs e)
        {
            if (_engine.CurrentProject == null)
            {
                MessageBox.Show("No project is currently open.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            await ValidateProject(_engine.CurrentProject);
        }

        private async void RefreshCurrentProject_Click(object sender, RoutedEventArgs e)
        {
            if (_engine.CurrentProject == null)
            {
                MessageBox.Show("No project is currently open.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                UpdateStatus("Refreshing current project...");
                var success = await _engine.RefreshCurrentProjectAsync();
                if (success)
                {
                    UpdateStatus("Project refreshed successfully");
                    DisplayProjectDetails(_engine.CurrentProject);
                }
                else
                {
                    UpdateStatus("Failed to refresh project");
                    MessageBox.Show("Failed to refresh project.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error refreshing project: {ex.Message}");
                MessageBox.Show($"Error refreshing project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CloseCurrentProject_Click(object sender, RoutedEventArgs e)
        {
            if (_engine.CurrentProject == null)
            {
                MessageBox.Show("No project is currently open.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                await _engine.CloseProjectAsync();
                UpdateStatus("Project closed");
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error closing project: {ex.Message}");
                MessageBox.Show($"Error closing project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show(
                "MonoGame Engine\n\n" +
                "A WPF-based engine for browsing, opening, and managing MonoGame projects.\n\n" +
                "Version: 1.0.0\n" +
                "Built with .NET 8.0",
                "About MonoGame Engine",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }

        private void DisplayProjectsList(List<MonoGameProject> projects)
        {
            projectsList.ItemsSource = projects;
            
            // Add click handlers to project items
            foreach (var item in projectsList.Items)
            {
                if (item is FrameworkElement element)
                {
                    element.MouseLeftButtonDown += (sender, e) =>
                    {
                        if (sender is FrameworkElement fe && fe.DataContext is MonoGameProject project)
                        {
                            DisplayProjectDetails(project);
                        }
                    };
                }
            }
        }

        private void DisplayProjectDetails(MonoGameProject? project)
        {
            if (project == null)
            {
                projectDetailsPanel.Children.Clear();
                projectDetailsPanel.Children.Add(new TextBlock
                {
                    Text = "No Project Selected",
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    Foreground = new SolidColorBrush(Colors.Gray),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 50, 0, 0)
                });
                return;
            }

            projectDetailsPanel.Children.Clear();

            // Project header
            var header = new TextBlock
            {
                Text = project.Name,
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20)
            };
            projectDetailsPanel.Children.Add(header);

            // Project info
            var infoPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };
            
            AddInfoRow(infoPanel, "Target Framework:", project.TargetFramework);
            AddInfoRow(infoPanel, "Output Type:", project.OutputType);
            AddInfoRow(infoPanel, "Directory:", project.DirectoryPath);
            AddInfoRow(infoPanel, "Last Modified:", project.LastModified.ToString("g"));
            AddInfoRow(infoPanel, "Source Files:", project.SourceFiles.Count.ToString());
            AddInfoRow(infoPanel, "Content Files:", project.ContentFiles.Count.ToString());
            AddInfoRow(infoPanel, "References:", project.References.Count.ToString());

            projectDetailsPanel.Children.Add(infoPanel);

            // Action buttons
            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 20, 0, 0) };
            
            var openButton = new Button
            {
                Content = "Open This Project",
                Style = FindResource("ModernButtonStyle") as Style,
                Margin = new Thickness(0, 0, 10, 0)
            };
            openButton.Click += async (sender, e) => await OpenProject(project);
            
            var validateButton = new Button
            {
                Content = "Validate Project",
                Style = FindResource("ModernButtonStyle") as Style,
                Background = new SolidColorBrush(Colors.Orange),
                Margin = new Thickness(0, 0, 10, 0)
            };
            validateButton.Click += async (sender, e) => await ValidateProject(project);
            
            buttonPanel.Children.Add(openButton);
            buttonPanel.Children.Add(validateButton);
            
            projectDetailsPanel.Children.Add(buttonPanel);
        }

        private void AddInfoRow(StackPanel panel, string label, string value)
        {
            var row = new Grid { Margin = new Thickness(0, 5, 0, 5) };
            row.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            row.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var labelBlock = new TextBlock
            {
                Text = label,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 10, 0),
                MinWidth = 120
            };
            Grid.SetColumn(labelBlock, 0);

            var valueBlock = new TextBlock
            {
                Text = value,
                TextWrapping = TextWrapping.Wrap
            };
            Grid.SetColumn(valueBlock, 1);

            row.Children.Add(labelBlock);
            row.Children.Add(valueBlock);
            panel.Children.Add(row);
        }

        private async Task OpenProject(MonoGameProject project)
        {
            try
            {
                UpdateStatus($"Opening project: {project.Name}...");
                var success = await _engine.OpenProjectAsync(project.FilePath);
                
                if (success)
                {
                    UpdateStatus($"Project opened: {project.Name}");
                    DisplayProjectDetails(_engine.CurrentProject);
                }
                else
                {
                    UpdateStatus($"Failed to open project: {project.Name}");
                    MessageBox.Show($"Failed to open project: {project.Name}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error opening project: {ex.Message}");
                MessageBox.Show($"Error opening project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ValidateProject(MonoGameProject project)
        {
            try
            {
                UpdateStatus($"Validating project: {project.Name}...");
                
                // First open the project if it's not already open
                if (_engine.CurrentProject?.FilePath != project.FilePath)
                {
                    await _engine.OpenProjectAsync(project.FilePath);
                }
                
                var issues = await _engine.ValidateCurrentProjectAsync();
                
                if (!issues.Any())
                {
                    MessageBox.Show("Project validation passed! No issues found.", "Validation Result", MessageBoxButton.OK, MessageBoxImage.Information);
                    UpdateStatus($"Project validation passed: {project.Name}");
                }
                else
                {
                    var message = $"Project validation found {issues.Count} issue(s):\n\n" + string.Join("\n", issues);
                    MessageBox.Show(message, "Validation Result", MessageBoxButton.OK, MessageBoxImage.Warning);
                    UpdateStatus($"Project validation found {issues.Count} issue(s): {project.Name}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error validating project: {ex.Message}");
                MessageBox.Show($"Error validating project: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnProjectOpened(object? sender, MonoGameProject project)
        {
            Dispatcher.Invoke(() =>
            {
                DisplayProjectDetails(project);
                UpdateStatus($"Project opened: {project.Name}");
            });
        }

        private void OnProjectClosed(object? sender, MonoGameProject project)
        {
            Dispatcher.Invoke(() =>
            {
                DisplayProjectDetails(null);
                UpdateStatus($"Project closed: {project.Name}");
            });
        }

        private void OnRecentProjectsChanged(object? sender, List<MonoGameProject> projects)
        {
            // Could update a recent projects list if needed
        }

        private void UpdateStatus(string message)
        {
            Dispatcher.Invoke(() =>
            {
                statusText.Text = message;
            });
        }

        protected override void OnClosed(EventArgs e)
        {
            _engine?.Dispose();
            base.OnClosed(e);
        }
    }
}