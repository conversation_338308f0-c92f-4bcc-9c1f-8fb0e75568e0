<Window x:Class="Monogame.Engine.UI.NewProjectWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Create MonoGame Project" Height="260" Width="520"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="140"/>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>

        <TextBlock Text="Project name:" Grid.Row="0" Grid.Column="0" Margin="0,0,8,8" VerticalAlignment="Center"/>
        <TextBox x:Name="txtName" Grid.Row="0" Grid.Column="1" Margin="0,0,8,8"/>

        <TextBlock Text="Location:" Grid.Row="1" Grid.Column="0" Margin="0,0,8,8" VerticalAlignment="Center"/>
        <TextBox x:Name="txtLocation" Grid.Row="1" Grid.Column="1" Margin="0,0,8,8"/>
        <Button Content="Browse..." Grid.Row="1" Grid.Column="2" Click="Browse_Click" Padding="12,6"/>

        <TextBlock Text="Template:" Grid.Row="2" Grid.Column="0" Margin="0,0,8,8" VerticalAlignment="Center"/>
        <ComboBox x:Name="cmbTemplate" Grid.Row="2" Grid.Column="1" Margin="0,0,8,8">
            <ComboBoxItem Content="DesktopGL" Tag="mgdesktopgl" IsSelected="True"/>
            <ComboBoxItem Content="Android" Tag="mgandroid"/>
            <ComboBoxItem Content="iOS" Tag="mgios"/>
            <ComboBoxItem Content="WindowsDX" Tag="mgwindowsdx"/>
        </ComboBox>

        <CheckBox x:Name="chkGit" Grid.Row="3" Grid.Column="1" Content="Initialize git repository"/>

        <StackPanel Grid.Row="4" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="Create" Click="Create_Click" Padding="16,8" Margin="0,0,8,0"/>
            <Button Content="Cancel" Click="Cancel_Click" Padding="16,8"/>
        </StackPanel>
    </Grid>
</Window>
