using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace Monogame.Engine.Core.Services
{
	public class ProjectCreationService
	{
		public class CreateProjectOptions
		{
			public string ProjectName { get; set; } = string.Empty;
			public string TargetDirectory { get; set; } = string.Empty;
			public string TemplateShortName { get; set; } = "mgdesktopgl"; // default DesktopGL
			public bool InitializeGitRepository { get; set; } = false;
		}

		public async Task<bool> EnsureMonoGameTemplatesInstalledAsync()
		{
			// Use modern dotnet new install command
			var result = await RunProcessAsync("dotnet", "new --list");
			if (!result.success)
			{
				return false;
			}

			if (result.output.IndexOf("MonoGame", StringComparison.OrdinalIgnoreCase) >= 0)
			{
				return true; // Templates already installed
			}

			var install = await RunProcessAsync("dotnet", "new --install MonoGame.Templates.CSharp");
			return install.success;
		}

		public async Task<(bool success, string? projectPath, string output)> CreateProjectAsync(CreateProjectOptions options)
		{
			if (string.IsNullOrWhiteSpace(options.ProjectName))
			{
				return (false, null, "Project name is required");
			}
			if (string.IsNullOrWhiteSpace(options.TargetDirectory))
			{
				return (false, null, "Target directory is required");
			}

			try
			{
				// Ensure target directory exists
				Directory.CreateDirectory(options.TargetDirectory);

				// Ensure templates installed
				var ensured = await EnsureMonoGameTemplatesInstalledAsync();
				if (!ensured)
				{
					return (false, null, "Failed to install MonoGame templates");
				}

				// Create the project using dotnet new
				var outputDir = Path.Combine(options.TargetDirectory, options.ProjectName);
				var args = $"new {options.TemplateShortName} -n \"{options.ProjectName}\" -o \"{outputDir}\"";
				var create = await RunProcessAsync("dotnet", args);
				if (!create.success)
				{
					return (false, null, create.output);
				}

				// Restore
				var restore = await RunProcessAsync("dotnet", "restore", outputDir);
				if (!restore.success)
				{
					// Still return success but include warning
					return (true, outputDir, "Project created but restore failed:\n" + restore.output);
				}

				// Optionally init git
				if (options.InitializeGitRepository)
				{
					await RunProcessAsync("git", "init", outputDir);
				}

				return (true, outputDir, create.output + "\n" + restore.output);
			}
			catch (Exception ex)
			{
				return (false, null, ex.Message);
			}
		}

		private static async Task<(bool success, string output)> RunProcessAsync(string fileName, string arguments, string? workingDirectory = null)
		{
			var psi = new ProcessStartInfo
			{
				FileName = fileName,
				Arguments = arguments,
				RedirectStandardOutput = true,
				RedirectStandardError = true,
				UseShellExecute = false,
				CreateNoWindow = true,
				WorkingDirectory = workingDirectory ?? Directory.GetCurrentDirectory()
			};

			var outputBuilder = new StringBuilder();
			var errorBuilder = new StringBuilder();

			using var process = new Process { StartInfo = psi, EnableRaisingEvents = true };
			process.OutputDataReceived += (s, e) => { if (e.Data != null) outputBuilder.AppendLine(e.Data); };
			process.ErrorDataReceived += (s, e) => { if (e.Data != null) errorBuilder.AppendLine(e.Data); };

			try
			{
				process.Start();
				process.BeginOutputReadLine();
				process.BeginErrorReadLine();
				await Task.Run(() => process.WaitForExit());
				var output = outputBuilder.ToString() + errorBuilder.ToString();
				return (process.ExitCode == 0, output);
			}
			catch (Exception ex)
			{
				return (false, ex.Message);
			}
		}
	}
}
