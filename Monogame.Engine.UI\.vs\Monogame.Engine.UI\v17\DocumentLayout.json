{"Version": 1, "WorkspaceRootPath": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|c:\\projects\\monogame\\engine\\monogame.engine.ui\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|c:\\projects\\monogame\\engine\\monogame.engine.ui\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|c:\\projects\\monogame\\engine\\monogame.engine.ui\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|solutionrelative:app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|c:\\projects\\monogame\\engine\\monogame.engine.ui\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|solutionrelative:assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|c:\\projects\\monogame\\engine\\monogame.engine.ui\\newprojectwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A4A755-0C6E-475C-8050-A980CF45A56C}|Monogame.Engine.UI.csproj|solutionrelative:newprojectwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml", "DocumentMoniker": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\App.xaml", "RelativeToolTip": "App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-21T01:35:17.634Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AssemblyInfo.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\AssemblyInfo.cs", "RelativeDocumentMoniker": "AssemblyInfo.cs", "ToolTip": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\AssemblyInfo.cs", "RelativeToolTip": "AssemblyInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-21T01:35:14.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "NewProjectWindow.xaml", "DocumentMoniker": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\NewProjectWindow.xaml", "RelativeDocumentMoniker": "NewProjectWindow.xaml", "ToolTip": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\NewProjectWindow.xaml", "RelativeToolTip": "NewProjectWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-21T01:35:07.098Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-18T22:58:21.907Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "C:\\Projects\\MonoGame\\Engine\\Monogame.Engine.UI\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAYwBkAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-18T22:58:18.274Z", "EditorCaption": ""}]}]}]}